import 'package:my_video/app_imports.dart';

class HomeController extends GetxController {
  final MovieRepository _movieRepository = GetIt.instance<MovieRepository>();
  final Logger _logger = Logger();

  // Only method needed - get category wise movies from showcatwise API
  Future<CategoryWiseResponse> getCategoryWiseMoviesFromAPI({
    int page = 1,
  }) async {
    try {
      return await _movieRepository.getCategoryWiseMovies(page: page);
    } catch (e) {
      _logger.e('Error getting category wise movies from API: $e');
      rethrow;
    }
  }

  Future<SearchMoviesResponse> searchMoviesFromAPI(
    String query, {
    int page = 1,
    int perPage = 20,
  }) async {
    try {
      return await _movieRepository.searchMovies(
        query,
        page: page,
        perPage: perPage,
      );
    } catch (e) {
      _logger.e('Error searching movies from API: $e');
      rethrow;
    }
  }

  Future<FilterResponse> getFiltersFromAPI() async {
    try {
      final response = await RestHelper.post('/showfilter');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        return FilterResponse.fromJson(responseData);
      } else {
        throw Exception('Failed to fetch filters: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Error getting filters from API: $e');
      rethrow;
    }
  }

  Future<MovieModel?> getMovieByIdFromAPI(String id) async {
    try {
      return await _movieRepository.getMovieById(id);
    } catch (e) {
      _logger.e('Error getting movie by ID from API: $e');
      rethrow;
    }
  }

  Future<List<MovieModel>> getRelatedMoviesFromAPI(
    String movieId,
    String category,
  ) async {
    try {
      return await _movieRepository.getRelatedMovies(movieId, category);
    } catch (e) {
      _logger.e('Error getting related movies from API: $e');
      rethrow;
    }
  }
}
