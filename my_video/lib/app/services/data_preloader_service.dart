import 'package:my_video/app_imports.dart';

class DataPreloaderService extends GetxController {
  static DataPreloaderService get instance => Get.find<DataPreloaderService>();

  final Logger _logger = Logger();
  final MovieRepository _movieRepository = GetIt.instance<MovieRepository>();

  // Preloaded data
  List<CategoryWiseData> _preloadedCategories = [];
  List<MovieModel> _preloadedFeaturedMovies = [];
  final Map<String, List<MovieModel>> _preloadedMoviesByCategory = {};

  // Loading states
  bool _isPreloading = false;
  bool _hasPreloadedData = false;
  ApiStatus _preloadStatus = ApiStatus.initial;

  // Getters
  List<CategoryWiseData> get preloadedCategories => _preloadedCategories;
  List<MovieModel> get preloadedFeaturedMovies => _preloadedFeaturedMovies;
  Map<String, List<MovieModel>> get preloadedMoviesByCategory =>
      _preloadedMoviesByCategory;
  bool get isPreloading => _isPreloading;
  bool get hasPreloadedData => _hasPreloadedData;
  ApiStatus get preloadStatus => _preloadStatus;

  /// Preload home page data during splash screen
  Future<void> preloadHomePageData() async {
    if (_hasPreloadedData || _isPreloading) {
      _logger.i('Home page data already preloaded or preloading in progress');
      return;
    }

    _isPreloading = true;
    _preloadStatus = ApiStatus.loading;
    update();

    try {
      _logger.i('Starting home page data preload...');

      // Check network connectivity first
      final hasInternet = await ConnectivityHelper.hasInternetConnection();
      if (!hasInternet) {
        _logger.w('No internet connection available for preloading');
        throw Exception('No internet connection available');
      }

      // First ensure auth token is available, then load data
      await _ensureAuthToken();

      // Small delay to ensure token is properly saved and available
      await Future.delayed(const Duration(milliseconds: 100));

      await _loadCategoryWiseData();

      _preloadStatus = ApiStatus.success;
      _hasPreloadedData = true;
      _logger.i(
        'Successfully preloaded home page data: ${_preloadedCategories.length} categories, ${_preloadedFeaturedMovies.length} featured movies',
      );
    } catch (e) {
      _logger.e('Error preloading home page data: $e');
      _preloadStatus = ApiStatus.error;

      // Try to load cached data as fallback
      try {
        await _loadCachedData();
        _logger.i('Successfully loaded cached data as fallback');
      } catch (cacheError) {
        _logger.e('Failed to load cached data as fallback: $cacheError');
        // Even if cache fails, we should continue - the home page will handle empty data gracefully
      }
    } finally {
      _isPreloading = false;
      update();
    }
  }

  /// Ensure authentication token is available
  Future<void> _ensureAuthToken() async {
    try {
      final existingToken = AppSharedPreference.getString('static_token');
      if (existingToken?.isNotEmpty == true) {
        _logger.i('Auth token already exists');
        return;
      }

      // Get unregistered user token
      _logger.i('Getting unregistered user token...');
      final response = await RestHelper.post('/unregisteredusertoken');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['status'] == 1) {
          final success = await AppSharedPreference.setString(
            'static_token',
            data['token'],
          );
          if (success) {
            _logger.i('Unregistered token obtained and saved successfully');
          } else {
            _logger.w('Failed to save unregistered token to SharedPreferences');
          }
        } else {
          _logger.w('Failed to get unregistered token: ${data['message']}');
        }
      } else {
        _logger.w('Failed to get unregistered token: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Error getting authentication token: $e');
    }
  }

  /// Load category wise data from API with retry logic
  Future<void> _loadCategoryWiseData() async {
    const maxRetries = 2;
    int retryCount = 0;

    while (retryCount <= maxRetries) {
      try {
        _logger.i(
          'Attempting to load category wise data... (attempt ${retryCount + 1}/${maxRetries + 1})',
        );
        final response = await _movieRepository.getCategoryWiseMovies(page: 1);

        _logger.i(
          'Category wise API response - Status: ${response.status}, Success: ${response.success}',
        );

        if (response.success && response.result?.isNotEmpty == true) {
          _preloadedCategories = response.result!;

          // Extract featured movies from the first category or create from all categories
          _preloadedFeaturedMovies = [];
          for (final categoryData in _preloadedCategories) {
            if (_preloadedFeaturedMovies.length < 5) {
              _preloadedFeaturedMovies.addAll(
                categoryData.data.take(5 - _preloadedFeaturedMovies.length),
              );
            }
          }

          // Populate movies by category map
          _preloadedMoviesByCategory.clear();
          for (final categoryData in _preloadedCategories) {
            _preloadedMoviesByCategory[categoryData.name] = categoryData.data;
          }

          _logger.i(
            'Preloaded category wise data: ${_preloadedCategories.length} categories',
          );
          return; // Success, exit retry loop
        } else {
          _logger.w(
            'Category wise API returned unsuccessful response: ${response.message}',
          );
          throw Exception(
            'API returned unsuccessful response: ${response.message}',
          );
        }
      } catch (e) {
        _logger.e(
          'Error preloading category wise data (attempt ${retryCount + 1}): $e',
        );

        if (retryCount < maxRetries) {
          retryCount++;
          final delaySeconds = retryCount * 2; // Exponential backoff: 2s, 4s
          _logger.i('Retrying in ${delaySeconds}s...');
          await Future.delayed(Duration(seconds: delaySeconds));
        } else {
          _logger.e('All retry attempts failed for category wise data');
          rethrow;
        }
      }
    }
  }

  /// Load cached data as fallback
  Future<void> _loadCachedData() async {
    try {
      final offlineManager = OfflineManager.instance;
      _preloadedFeaturedMovies = offlineManager.getCachedFeaturedMovies();

      // For now, create empty category wise data from cached data
      _preloadedCategories = [];
      _preloadedMoviesByCategory.clear();

      _logger.i(
        'Loaded cached data for preload: ${_preloadedFeaturedMovies.length} featured movies',
      );
      _preloadStatus = ApiStatus.success;
      _hasPreloadedData = true;
    } catch (e) {
      _logger.e('Error loading cached data for preload: $e');
      _preloadStatus = ApiStatus.error;
    }
  }

  /// Clear preloaded data (useful for refresh scenarios)
  void clearPreloadedData() {
    _preloadedCategories.clear();
    _preloadedFeaturedMovies.clear();
    _preloadedMoviesByCategory.clear();
    _hasPreloadedData = false;
    _preloadStatus = ApiStatus.initial;
    update();
    _logger.i('Cleared preloaded data');
  }
}
