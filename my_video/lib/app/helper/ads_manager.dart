import 'package:my_video/app_imports.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdsManager {
  static final Logger _logger = Logger();
  static bool _isInitialized = false;
  static bool _isPremiumUser = false;

  // Ad Unit IDs (Test IDs - Replace with real ones in production)
  // Note: Replace these with your actual Ad Unit IDs when deploying in ios ca-app-pub-3940256099942544~1458002511

  static const String _bannerAdUnitId =
      'ca-app-pub-3940256099942544/6300978111';
  static const String _interstitialAdUnitId =
      'ca-app-pub-3940256099942544/1033173712';
  static const String _nativeAdUnitId =
      'ca-app-pub-3940256099942544/2247696110';

  // Ad instances
  static BannerAd? _bannerAd;
  static InterstitialAd? _interstitialAd;
  static NativeAd? _nativeAd;

  // Ad loading states
  static bool _isBannerAdLoaded = false;
  static bool _isInterstitialAdLoaded = false;
  static bool _isNativeAdLoaded = false;

  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _logger.i('Initializing Google Mobile Ads...');

      // Skip ads initialization on iOS for now to prevent crashes
      if (Platform.isIOS) {
        _logger.i('Skipping ads initialization on iOS');
        _isInitialized = true;
        return;
      }

      await MobileAds.instance.initialize().timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          _logger.w('Google Mobile Ads initialization timed out');
          throw TimeoutException(
            'Ads initialization timeout',
            const Duration(seconds: 10),
          );
        },
      );

      _isInitialized = true;
      _logger.i('Google Mobile Ads initialized successfully');

      // Check premium status
      _updatePremiumStatus();

      // Pre-load ads if not premium
      if (!_isPremiumUser) {
        await _preloadAds();
      }
    } catch (e) {
      _logger.e('Error initializing Google Mobile Ads: $e');
      // Don't throw the error, just log it and continue without ads
      _isInitialized = false;
    }
  }

  static void _updatePremiumStatus() {
    // Set to false by default to show ads for testing
    _isPremiumUser = false;
    _logger.i('Premium status: $_isPremiumUser');
  }

  static Future<void> _preloadAds() async {
    await Future.wait([
      _loadBannerAd(),
      _loadInterstitialAd(),
      _loadNativeAd(),
    ]);
  }

  // Banner Ad Methods
  static final List<BannerAd> _bannerAds = [];
  static final List<bool> _bannerAdsLoaded = [];
  static final Map<String, BannerAd> _categoryBannerAds = {};
  static final Map<String, bool> _categoryBannerAdsLoaded = {};

  static Future<void> _loadBannerAd() async {
    if (_isPremiumUser) return;

    // Load multiple banner ads for different positions (increased to 10 for more categories)
    for (int i = 0; i < 10; i++) {
      try {
        final bannerAd = BannerAd(
          adUnitId: _bannerAdUnitId,
          size: AdSize.banner,
          request: const AdRequest(),
          listener: BannerAdListener(
            onAdLoaded: (ad) {
              _bannerAdsLoaded[i] = true;
              _logger.i('Banner ad $i loaded successfully');
            },
            onAdFailedToLoad: (ad, error) {
              _bannerAdsLoaded[i] = false;
              ad.dispose();
              _logger.e('Banner ad $i failed to load: $error');
            },
            onAdOpened: (ad) => _logger.i('Banner ad $i opened'),
            onAdClosed: (ad) => _logger.i('Banner ad $i closed'),
          ),
        );

        _bannerAds.add(bannerAd);
        _bannerAdsLoaded.add(false);
        await bannerAd.load();
      } catch (e) {
        _logger.e('Error loading banner ad $i: $e');
        _bannerAdsLoaded.add(false);
      }
    }
  }

  static Widget? getBannerAdWidget({int index = 0, String? uniqueId}) {
    if (_isPremiumUser) return null;

    // If uniqueId is provided, create a fresh ad widget for this specific use
    if (uniqueId != null) {
      return _createFreshBannerAdWidget(uniqueId);
    }

    // Fallback to indexed ads
    if (index >= _bannerAds.length ||
        index >= _bannerAdsLoaded.length ||
        !_bannerAdsLoaded[index]) {
      return null;
    }

    return SizedBox(
      key: ValueKey('banner_ad_$index'),
      width: _bannerAds[index].size.width.toDouble(),
      height: _bannerAds[index].size.height.toDouble(),
      child: AdWidget(ad: _bannerAds[index]),
    );
  }

  static Widget _createFreshBannerAdWidget(String uniqueId) {
    // Create a completely fresh banner ad for this specific widget
    // This ensures no AdWidget reuse issues
    return FreshBannerAdWidget(
      key: ValueKey('fresh_ad_$uniqueId'),
      adUnitId: _bannerAdUnitId,
      uniqueId: uniqueId,
    );
  }

  // Interstitial Ad Methods
  static Future<void> _loadInterstitialAd() async {
    if (_isPremiumUser) return;

    try {
      await InterstitialAd.load(
        adUnitId: _interstitialAdUnitId,
        request: const AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (ad) {
            _interstitialAd = ad;
            _isInterstitialAdLoaded = true;
            _logger.i('Interstitial ad loaded successfully');

            ad.fullScreenContentCallback = FullScreenContentCallback(
              onAdShowedFullScreenContent: (ad) =>
                  _logger.i('Interstitial ad showed'),
              onAdDismissedFullScreenContent: (ad) {
                ad.dispose();
                _isInterstitialAdLoaded = false;
                _loadInterstitialAd(); // Preload next ad
                _logger.i('Interstitial ad dismissed');
              },
              onAdFailedToShowFullScreenContent: (ad, error) {
                ad.dispose();
                _isInterstitialAdLoaded = false;
                _logger.e('Interstitial ad failed to show: $error');
              },
            );
          },
          onAdFailedToLoad: (error) {
            _isInterstitialAdLoaded = false;
            _logger.e('Interstitial ad failed to load: $error');
          },
        ),
      );
    } catch (e) {
      _logger.e('Error loading interstitial ad: $e');
    }
  }

  static Future<void> showInterstitialAd({VoidCallback? onAdClosed}) async {
    if (_isPremiumUser) {
      onAdClosed?.call();
      return;
    }

    // Skip ads on iOS for now to prevent crashes
    if (Platform.isIOS) {
      _logger.i('Skipping interstitial ad on iOS');
      onAdClosed?.call();
      return;
    }

    if (_isInterstitialAdLoaded && _interstitialAd != null) {
      try {
        await _interstitialAd!.show();
        onAdClosed?.call();
      } catch (e) {
        _logger.e('Error showing interstitial ad: $e');
        onAdClosed?.call();
      }
    } else {
      _logger.w('Interstitial ad not ready');
      onAdClosed?.call();
    }
  }

  // Native Ad Methods
  static Future<void> _loadNativeAd() async {
    if (_isPremiumUser) return;

    try {
      _nativeAd = NativeAd(
        adUnitId: _nativeAdUnitId,
        request: const AdRequest(),
        listener: NativeAdListener(
          onAdLoaded: (ad) {
            _isNativeAdLoaded = true;
            _logger.i('Native ad loaded successfully');
          },
          onAdFailedToLoad: (ad, error) {
            _isNativeAdLoaded = false;
            ad.dispose();
            _logger.e('Native ad failed to load: $error');
          },
          onAdOpened: (ad) => _logger.i('Native ad opened'),
          onAdClosed: (ad) => _logger.i('Native ad closed'),
        ),
        nativeTemplateStyle: NativeTemplateStyle(
          templateType: TemplateType.medium,
          mainBackgroundColor: AppColorConstants.cardColor,
          cornerRadius: MySize.radius(12),
          callToActionTextStyle: NativeTemplateTextStyle(
            textColor: AppColorConstants.textPrimary,
            backgroundColor: AppColorConstants.primaryColor,
            style: NativeTemplateFontStyle.bold,
            size: MySize.fontSize(14),
          ),
          primaryTextStyle: NativeTemplateTextStyle(
            textColor: AppColorConstants.textPrimary,
            style: NativeTemplateFontStyle.bold,
            size: MySize.fontSize(16),
          ),
          secondaryTextStyle: NativeTemplateTextStyle(
            textColor: AppColorConstants.textSecondary,
            style: NativeTemplateFontStyle.normal,
            size: MySize.fontSize(14),
          ),
        ),
      );

      await _nativeAd!.load();
    } catch (e) {
      _logger.e('Error loading native ad: $e');
    }
  }

  static Widget? getNativeAdWidget() {
    if (_isPremiumUser || !_isNativeAdLoaded || _nativeAd == null) {
      return null;
    }

    return Container(
      height: MySize.height(200),
      margin: EdgeInsets.symmetric(horizontal: MySize.width(16)),
      child: AdWidget(ad: _nativeAd!),
    );
  }

  // Ad Placeholder Widgets
  static Widget buildAdPlaceholder({
    double? width,
    double? height,
    String? label,
  }) {
    if (_isPremiumUser) return const SizedBox.shrink();

    return Container(
      width: width ?? MySize.width(140),
      height: height ?? MySize.height(200),
      margin: EdgeInsets.only(right: MySize.width(12)),
      decoration: BoxDecoration(
        color: AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.radius(12)),
        border: Border.all(color: AppColorConstants.dividerColor, width: 1),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.ads_click_outlined,
            size: MySize.height(40),
            color: AppColorConstants.textHint,
          ),
          Space.height(8),
          AppText(
            text: label ?? 'Advertisement',
            fontSize: MySize.fontSize(12),
            color: AppColorConstants.textHint,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Utility Methods
  static bool get isPremiumUser => _isPremiumUser;
  static bool get shouldShowAds => !_isPremiumUser;

  static void updatePremiumStatus(bool isPremium) {
    _isPremiumUser = isPremium;
    _logger.i('Premium status updated: $_isPremiumUser');

    if (_isPremiumUser) {
      _disposeAllAds();
    } else {
      _preloadAds();
    }
  }

  static void _disposeAllAds() {
    // Dispose all banner ads
    for (final bannerAd in _bannerAds) {
      bannerAd.dispose();
    }
    _bannerAds.clear();
    _bannerAdsLoaded.clear();

    // Dispose all category-specific banner ads
    for (final bannerAd in _categoryBannerAds.values) {
      bannerAd.dispose();
    }
    _categoryBannerAds.clear();
    _categoryBannerAdsLoaded.clear();

    _interstitialAd?.dispose();
    _nativeAd?.dispose();

    _bannerAd = null;
    _interstitialAd = null;
    _nativeAd = null;

    _isBannerAdLoaded = false;
    _isInterstitialAdLoaded = false;
    _isNativeAdLoaded = false;

    _logger.i('All ads disposed');
  }

  static void dispose() {
    _disposeAllAds();
    _isInitialized = false;
  }
}

// Fresh Banner Ad Widget that creates its own BannerAd instance
class FreshBannerAdWidget extends StatefulWidget {
  final String adUnitId;
  final String uniqueId;

  const FreshBannerAdWidget({
    super.key,
    required this.adUnitId,
    required this.uniqueId,
  });

  @override
  State<FreshBannerAdWidget> createState() => _FreshBannerAdWidgetState();
}

class _FreshBannerAdWidgetState extends State<FreshBannerAdWidget> {
  BannerAd? _bannerAd;
  bool _isLoaded = false;
  final Logger _logger = Logger();

  @override
  void initState() {
    super.initState();
    _loadAd();
  }

  void _loadAd() {
    _bannerAd = BannerAd(
      adUnitId: widget.adUnitId,
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          if (mounted) {
            setState(() {
              _isLoaded = true;
            });
          }
          _logger.i('Fresh banner ad loaded for: ${widget.uniqueId}');
        },
        onAdFailedToLoad: (ad, error) {
          _logger.e(
            'Fresh banner ad failed to load for ${widget.uniqueId}: $error',
          );
          ad.dispose();
          if (mounted) {
            setState(() {
              _bannerAd = null;
            });
          }
        },
        onAdOpened: (ad) =>
            _logger.i('Fresh banner ad opened: ${widget.uniqueId}'),
        onAdClosed: (ad) =>
            _logger.i('Fresh banner ad closed: ${widget.uniqueId}'),
      ),
    );
    _bannerAd!.load();
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_bannerAd != null && _isLoaded) {
      return SizedBox(
        width: _bannerAd!.size.width.toDouble(),
        height: _bannerAd!.size.height.toDouble(),
        child: AdWidget(ad: _bannerAd!),
      );
    } else {
      return AdsManager.buildAdPlaceholder(
        width: AdSize.banner.width.toDouble(),
        height: AdSize.banner.height.toDouble(),
        label: 'Loading Ad...',
      );
    }
  }
}
