import 'package:my_video/app_imports.dart';

class RestHelper {
  static final Client _client = Client();
  static const Duration _timeout = Duration(seconds: 30);

  static Map<String, String> get _defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  static Map<String, String> _getHeaders({
    Map<String, String>? additionalHeaders,
  }) {
    final headers = Map<String, String>.from(_defaultHeaders);

    // Add authentication header if available
    final userToken = AppSharedPreference.getUserToken();
    final unregisteredToken = AppSharedPreference.getUnregisteredToken();
    final staticToken = AppSharedPreference.getString('static_token');

    // Priority: user token > unregistered token > static token
    if (userToken != null && userToken.isNotEmpty) {
      headers['auth'] = userToken;
    } else if (unregisteredToken != null && unregisteredToken.isNotEmpty) {
      headers['auth'] = unregisteredToken;
    } else if (staticToken != null && staticToken.isNotEmpty) {
      headers['auth'] = staticToken;
    }

    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }
    return headers;
  }

  static Future<Response> get(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final uri = _buildUri(endpoint, queryParameters);
      AppHelper.logDebug('GET Request: $uri');

      final response = await _client
          .get(uri, headers: _getHeaders(additionalHeaders: headers))
          .timeout(_timeout);

      AppHelper.logDebug(
        'GET Response: ${response.statusCode} - ${response.body}',
      );
      return response;
    } catch (e) {
      AppHelper.logError('GET Request Error', e);
      rethrow;
    }
  }

  static Future<Response> post(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final uri = _buildUri(endpoint, queryParameters);
      final jsonBody = body != null ? jsonEncode(body) : null;

      AppHelper.logDebug('POST Request: $uri');
      AppHelper.logDebug('POST Body: $jsonBody');

      final response = await _client
          .post(
            uri,
            headers: _getHeaders(additionalHeaders: headers),
            body: jsonBody,
          )
          .timeout(_timeout);

      AppHelper.logDebug(
        'POST Response: ${response.statusCode} - ${response.body}',
      );
      return response;
    } catch (e) {
      AppHelper.logError('POST Request Error', e);
      rethrow;
    }
  }

  static Future<Response> put(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final uri = _buildUri(endpoint, queryParameters);
      final jsonBody = body != null ? jsonEncode(body) : null;

      AppHelper.logDebug('PUT Request: $uri');
      AppHelper.logDebug('PUT Body: $jsonBody');

      final response = await _client
          .put(
            uri,
            headers: _getHeaders(additionalHeaders: headers),
            body: jsonBody,
          )
          .timeout(_timeout);

      AppHelper.logDebug(
        'PUT Response: ${response.statusCode} - ${response.body}',
      );
      return response;
    } catch (e) {
      AppHelper.logError('PUT Request Error', e);
      rethrow;
    }
  }

  static Future<Response> delete(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final uri = _buildUri(endpoint, queryParameters);
      AppHelper.logDebug('DELETE Request: $uri');

      final response = await _client
          .delete(uri, headers: _getHeaders(additionalHeaders: headers))
          .timeout(_timeout);

      AppHelper.logDebug(
        'DELETE Response: ${response.statusCode} - ${response.body}',
      );
      return response;
    } catch (e) {
      AppHelper.logError('DELETE Request Error', e);
      rethrow;
    }
  }

  static Uri _buildUri(String endpoint, Map<String, dynamic>? queryParameters) {
    final baseUrl = AppConfig.instance.baseUrl;
    // Remove leading slash from endpoint if baseUrl already ends with slash
    final cleanEndpoint = endpoint.startsWith('/')
        ? endpoint.substring(1)
        : endpoint;
    final cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl : '$baseUrl/';
    final uri = Uri.parse('$cleanBaseUrl$cleanEndpoint');

    if (queryParameters != null && queryParameters.isNotEmpty) {
      return uri.replace(
        queryParameters: queryParameters.map(
          (key, value) => MapEntry(key, value.toString()),
        ),
      );
    }

    return uri;
  }

  static void dispose() {
    _client.close();
  }
}
