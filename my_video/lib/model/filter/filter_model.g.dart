// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'filter_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FilterResponse _$FilterResponseFromJson(Map<String, dynamic> json) =>
    FilterResponse(
      status: (json['status'] as num).toInt(),
      message: json['message'] as String,
      categoryNames:
          (json['category'] as List<dynamic>).map((e) => e as String).toList(),
      languageNames:
          (json['language'] as List<dynamic>).map((e) => e as String).toList(),
      genreNames:
          (json['genre'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$FilterResponseToJson(FilterResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'message': instance.message,
      'category': instance.categoryNames,
      'language': instance.languageNames,
      'genre': instance.genreNames,
    };

FilterCategory _$FilterCategoryFromJson(Map<String, dynamic> json) =>
    FilterCategory(
      catId: (json['cat_id'] as num).toInt(),
      catName: json['cat_name'] as String,
      catImg: json['cat_img'] as String?,
    );

Map<String, dynamic> _$FilterCategoryToJson(FilterCategory instance) =>
    <String, dynamic>{
      'cat_id': instance.catId,
      'cat_name': instance.catName,
      'cat_img': instance.catImg,
    };

FilterLanguage _$FilterLanguageFromJson(Map<String, dynamic> json) =>
    FilterLanguage(
      langId: (json['lang_id'] as num).toInt(),
      language: json['language'] as String,
    );

Map<String, dynamic> _$FilterLanguageToJson(FilterLanguage instance) =>
    <String, dynamic>{
      'lang_id': instance.langId,
      'language': instance.language,
    };

FilterGenre _$FilterGenreFromJson(Map<String, dynamic> json) => FilterGenre(
      genreId: (json['genre_id'] as num).toInt(),
      genre: json['genre'] as String,
    );

Map<String, dynamic> _$FilterGenreToJson(FilterGenre instance) =>
    <String, dynamic>{
      'genre_id': instance.genreId,
      'genre': instance.genre,
    };
