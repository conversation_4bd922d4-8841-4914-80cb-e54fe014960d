import 'package:json_annotation/json_annotation.dart';

part 'filter_model.g.dart';

@JsonSerializable()
class FilterResponse {
  @Json<PERSON>ey(name: 'status')
  final int status;

  @Json<PERSON>ey(name: 'message')
  final String message;

  @Json<PERSON>ey(name: 'category')
  final List<String> categoryNames;

  @Json<PERSON>ey(name: 'language')
  final List<String> languageNames;

  @JsonKey(name: 'genre')
  final List<String> genreNames;

  FilterResponse({
    required this.status,
    required this.message,
    required this.categoryNames,
    required this.languageNames,
    required this.genreNames,
  });

  factory FilterResponse.fromJson(Map<String, dynamic> json) =>
      _$FilterResponseFromJson(json);

  Map<String, dynamic> toJson() => _$FilterResponseToJson(this);

  bool get success => status == 1;

  // Convert string arrays to filter objects for UI
  List<FilterCategory> get categories => categoryNames
      .asMap()
      .entries
      .map(
        (entry) => FilterCategory(catId: entry.key + 1, catName: entry.value),
      )
      .toList();

  List<FilterLanguage> get languages => languageNames
      .asMap()
      .entries
      .map(
        (entry) => FilterLanguage(langId: entry.key + 1, language: entry.value),
      )
      .toList();

  List<FilterGenre> get genres => genreNames
      .asMap()
      .entries
      .map((entry) => FilterGenre(genreId: entry.key + 1, genre: entry.value))
      .toList();
}

@JsonSerializable()
class FilterCategory {
  @JsonKey(name: 'cat_id')
  final int catId;

  @JsonKey(name: 'cat_name')
  final String catName;

  @JsonKey(name: 'cat_img')
  final String? catImg;

  FilterCategory({required this.catId, required this.catName, this.catImg});

  factory FilterCategory.fromJson(Map<String, dynamic> json) =>
      _$FilterCategoryFromJson(json);

  Map<String, dynamic> toJson() => _$FilterCategoryToJson(this);

  @override
  String toString() {
    return 'FilterCategory(catId: $catId, catName: $catName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FilterCategory && other.catId == catId;
  }

  @override
  int get hashCode => catId.hashCode;
}

@JsonSerializable()
class FilterLanguage {
  @JsonKey(name: 'lang_id')
  final int langId;

  @JsonKey(name: 'language')
  final String language;

  FilterLanguage({required this.langId, required this.language});

  factory FilterLanguage.fromJson(Map<String, dynamic> json) =>
      _$FilterLanguageFromJson(json);

  Map<String, dynamic> toJson() => _$FilterLanguageToJson(this);

  @override
  String toString() {
    return 'FilterLanguage(langId: $langId, language: $language)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FilterLanguage && other.langId == langId;
  }

  @override
  int get hashCode => langId.hashCode;
}

@JsonSerializable()
class FilterGenre {
  @JsonKey(name: 'genre_id')
  final int genreId;

  @JsonKey(name: 'genre')
  final String genre;

  FilterGenre({required this.genreId, required this.genre});

  factory FilterGenre.fromJson(Map<String, dynamic> json) =>
      _$FilterGenreFromJson(json);

  Map<String, dynamic> toJson() => _$FilterGenreToJson(this);

  @override
  String toString() {
    return 'FilterGenre(genreId: $genreId, genre: $genre)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FilterGenre && other.genreId == genreId;
  }

  @override
  int get hashCode => genreId.hashCode;
}

// Model for selected filters
class SelectedFilters {
  final List<FilterCategory> selectedCategories;
  final List<FilterLanguage> selectedLanguages;
  final List<FilterGenre> selectedGenres;

  SelectedFilters({
    this.selectedCategories = const [],
    this.selectedLanguages = const [],
    this.selectedGenres = const [],
  });

  bool get hasAnyFilter =>
      selectedCategories.isNotEmpty ||
      selectedLanguages.isNotEmpty ||
      selectedGenres.isNotEmpty;

  SelectedFilters copyWith({
    List<FilterCategory>? selectedCategories,
    List<FilterLanguage>? selectedLanguages,
    List<FilterGenre>? selectedGenres,
  }) {
    return SelectedFilters(
      selectedCategories: selectedCategories ?? this.selectedCategories,
      selectedLanguages: selectedLanguages ?? this.selectedLanguages,
      selectedGenres: selectedGenres ?? this.selectedGenres,
    );
  }

  @override
  String toString() {
    return 'SelectedFilters(categories: ${selectedCategories.length}, languages: ${selectedLanguages.length}, genres: ${selectedGenres.length})';
  }
}
