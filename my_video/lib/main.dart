import 'package:my_video/app_imports.dart';

final getIt = GetIt.instance;

void initRepositories() {
  getIt.registerSingleton<AuthenticationRepository>(
    AuthenticationRepositoryImpl(),
  );
  getIt.registerSingleton<MovieRepository>(MovieRepositoryImpl());
  Get.put(OfflineManager());
  Get.put(DataPreloaderService(), permanent: true);
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await AppSharedPreference.init();

  await HiveHelper.init();

  await AdsManager.initialize();

  // await MockDataHelper.initializeMockData(); // Disabled for now

  // Configure for your server environment
  // Options: 'local', 'local_network', 'staging', 'production', 'custom'
  AppConfig.create(
    appName: 'MyVideo - Movie Streaming',
    flavor: Flavor.prod,
    environment: 'production', // Change this to match your server setup
  );
  initRepositories();
  HttpOverrides.global = MyHttpOverrides();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final botToastBuilder = BotToastInit();
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus!.unfocus(),
      child: GetMaterialApp.router(
        title: 'MyVideo - Movie Streaming',
        theme: AppTheme.darkTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.dark,
        routerDelegate: router.routerDelegate,
        backButtonDispatcher: router.backButtonDispatcher,
        routeInformationParser: router.routeInformationParser,
        routeInformationProvider: router.routeInformationProvider,
        debugShowCheckedModeBanner: false,
        defaultTransition: Transition.rightToLeft,
        locale: Get.deviceLocale,
        transitionDuration: const Duration(microseconds: 800),
        navigatorObservers: <NavigatorObserver>[BotToastNavigatorObserver()],
        scrollBehavior: ScrollHelper(),
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(
              context,
            ).copyWith(textScaler: TextScaler.noScaling),
            child: child = botToastBuilder(context, child),
          );
        },
      ),
    );
  }
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}
